{"format_version": "1.21.60", "minecraft:block": {"description": {"identifier": "gravestone_death_path:gravestone_zombie", "menu_category": {"category": "construction"}, "traits": {"minecraft:placement_direction": {"enabled_states": ["minecraft:cardinal_direction"]}}, "states": {"gravestone_death_path:is_gravestone": [true, false]}}, "components": {"tag:gravestone_death_path:gravestone": {}, "tag:minecraft:is_shovel_item_destructible": {}, "minecraft:geometry": {"identifier": "geometry.gravestone_death_path.gravestone"}, "minecraft:material_instances": {"*": {"render_method": "alpha_test", "texture": "gravestone_dirt"}, "stone": {"render_method": "alpha_test", "texture": "gravestone_stone"}, "text": {"render_method": "alpha_test", "texture": "gravestone_text"}, "skull": {"render_method": "alpha_test", "texture": "gravestone_zombie"}}, "minecraft:selection_box": {"origin": [-6.5, 0, -7.5], "size": [13, 4, 15]}, "minecraft:collision_box": {"origin": [-6.5, 0, -7.5], "size": [13, 4, 15]}, "minecraft:destructible_by_explosion": {"explosion_resistance": 1}, "minecraft:destructible_by_mining": {"seconds_to_destroy": 1}, "minecraft:light_dampening": 0}, "permutations": [{"condition": "query.block_state('minecraft:cardinal_direction') == 'south'", "components": {"minecraft:transformation": {"rotation": [0, 180, 0]}}}, {"condition": "query.block_state('minecraft:cardinal_direction') == 'west'", "components": {"minecraft:transformation": {"rotation": [0, 90, 0]}}}, {"condition": "query.block_state('minecraft:cardinal_direction') == 'east'", "components": {"minecraft:transformation": {"rotation": [0, -90, 0]}}}]}}