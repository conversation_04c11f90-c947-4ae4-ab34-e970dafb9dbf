{
  "format_version": "1.21.60",
  "minecraft:block": {
    "description": {
      "identifier": "gravestone_death_path:gravestone",
      "menu_category": { "category": "construction" },
      "traits": { "minecraft:placement_direction": { "enabled_states": [ "minecraft:cardinal_direction" ] } },
      "states": { "gravestone_death_path:dimension": [0, 1, 2] }
    },
    "components": {
      "tag:gravestone_death_path:gravestone": {},
      "tag:minecraft:is_shovel_item_destructible": {},
      "minecraft:geometry": {
        "identifier": "geometry.gravestone_death_path.gravestone",
        "bone_visibility": { "skull": false }
      },
      "minecraft:material_instances": {
        "*": { "render_method": "alpha_test", "texture": "gravestone_dirt" },
        "stone": { "render_method": "alpha_test", "texture": "gravestone_stone" },
        "text": { "render_method": "alpha_test", "texture": "gravestone_text" }
      },
      "minecraft:selection_box": { "origin": [-6.5, 0, -7.5], "size": [13, 4, 15] },
      "minecraft:collision_box": { "origin": [-6.5, 0, -7.5], "size": [13, 4, 15] },
      "minecraft:destructible_by_explosion": { "explosion_resistance": 1 },
      "minecraft:destructible_by_mining": { "seconds_to_destroy": 1 },
      "minecraft:light_dampening": 0
    },
    "permutations": [
      {
        "condition": "query.block_state('gravestone_death_path:dimension') == 1",
        "components": {
          "minecraft:material_instances": {
            "*": { "render_method": "alpha_test", "texture": "gravestone_soul_sand" },
            "stone": { "render_method": "alpha_test", "texture": "gravestone_basalt" },
            "text": { "render_method": "alpha_test", "texture": "gravestone_text" }
          }
        }
      },
      {
        "condition": "query.block_state('gravestone_death_path:dimension') == 2",
        "components": {
          "minecraft:material_instances": {
            "*": { "render_method": "alpha_test", "texture": "gravestone_end_stone" },
            "stone": { "render_method": "alpha_test", "texture": "gravestone_stone" },
            "text": { "render_method": "alpha_test", "texture": "gravestone_text" }
          }
        }
      },
      {
        "condition": "query.block_state('minecraft:cardinal_direction') == 'south'",
        "components": { "minecraft:transformation": { "rotation": [0, 180, 0] } }
      },
      {
        "condition": "query.block_state('minecraft:cardinal_direction') == 'west'",
        "components": { "minecraft:transformation": { "rotation": [0, 90, 0] } }
      },
      {
        "condition": "query.block_state('minecraft:cardinal_direction') == 'east'",
        "components": { "minecraft:transformation": { "rotation": [0, -90, 0] } }
      }
    ]
  }
}
// "item_specif_speed": [
  // { "item": { "tags": "q.all_tags('minecraft:is_shovel', 'minecraft:wooden_tier')" }, "destroy_speed": 2 },
  // { "item": { "tags": "q.all_tags('minecraft:is_shovel', 'minecraft:stone_tier')" }, "destroy_speed": 1.15 },
  // { "item": { "tags": "q.all_tags('minecraft:is_shovel', 'minecraft:iron_tier')" }, "destroy_speed": 1.15 },
  // { "item": { "tags": "q.all_tags('minecraft:is_shovel', 'minecraft:golden_tier')" }, "destroy_speed": 1.15 },
  // { "item": { "tags": "q.all_tags('minecraft:is_shovel', 'minecraft:diamond_tier')" }, "destroy_speed": 0.25 },
  // { "item": { "tags": "q.all_tags('minecraft:is_shovel', 'minecraft:netherite_tier')" }, "destroy_speed": 1.15 }
// ]