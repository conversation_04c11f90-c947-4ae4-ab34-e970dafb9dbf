import { world, system } from "@minecraft/server";
import { apiInventory } from "../lib/player/inventory";
import { deathPath } from "../functions/locator";
const denyList = new Map();
system.afterEvents.scriptEventReceive.subscribe(ev => {
    const execute = scriptEventList[ev.id];
    if (execute)
        execute(ev);
});
const scriptEventList = new class scriptEventList {
    async "gravestone_death_path:drop_all"(callback) {
        const { sourceEntity: entity } = callback;
        if (entity && !denyList.has(entity.id)) {
            const id = entity.id;
            denyList.set(id, true);
            await system.waitTicks(5);
            const newEntity = world.getEntity(id);
            if (!newEntity)
                return;
            const block = newEntity.dimension.getBlock(newEntity.location);
            if (block == undefined || block.getTags().includes("gravestone_death_path:gravestone"))
                return;
            const t = await apiInventory.dropInventory(id);
            if (!t)
                return;
            denyList.delete(id);
            return newEntity.remove();
        }
    }
    "gravestone_death_path:ghost_move"(callback) {
        const { sourceEntity: entity } = callback;
        if (entity)
            deathPath.continueMoving(entity);
    }
};
