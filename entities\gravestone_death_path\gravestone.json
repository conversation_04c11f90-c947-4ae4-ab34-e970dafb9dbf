{"format_version": "1.21.60", "minecraft:entity": {"description": {"identifier": "gravestone_death_path:gravestone_entity", "is_spawnable": true, "is_summonable": true, "is_experimental": false, "properties": {"gravestone_death_path:xp": {"type": "int", "range": [0, 999999], "default": 0}}}, "component_groups": {"gravestone_death_path:in_gravestone": {"minecraft:environment_sensor": {"triggers": [{"filters": [{"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_bogged"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_skeleton"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_stray"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_wither_skeleton"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_nether"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_nether_bogged"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_nether_skeleton"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_nether_stray"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_nether_wither_skeleton"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_end"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_end_bogged"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_end_skeleton"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_end_stray"}, {"test": "in_block", "subject": "self", "operator": "!=", "value": "gravestone_death_path:gravestone_end_wither_skeleton"}], "event": "gravestone_death_path:drop_all"}]}}}, "components": {"minecraft:type_family": {"family": ["gravestone"]}, "minecraft:inventory": {"inventory_size": 100}, "minecraft:collision_box": {"width": 0.001, "height": 0.001}, "minecraft:physics": {"has_collision": false, "has_gravity": false}, "minecraft:damage_sensor": {"triggers": [{"cause": "all", "deals_damage": "no"}]}, "minecraft:nameable": {"allow_name_tag_renaming": false}, "minecraft:fire_immune": true, "minecraft:persistent": {}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["gravestone_death_path:in_gravestone"]}}, "gravestone_death_path:drop_all": {"queue_command": {"command": ["scriptevent gravestone_death_path:drop_all"]}}, "gravestone_death_path:add_in_gravestone": {"add": {"component_groups": ["gravestone_death_path:in_gravestone"]}}, "gravestone_death_path:remove_in_gravestone": {"remove": {"component_groups": ["gravestone_death_path:in_gravestone"]}}}}}