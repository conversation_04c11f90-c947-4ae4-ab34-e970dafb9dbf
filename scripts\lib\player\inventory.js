import { world, system } from "@minecraft/server";
import { apiEquippable } from "./equippable";
export const apiInventory = new class apiInventory {
    getItems(inv, ids) {
        const items = [];
        for (let i = 0; i < inv.size; i++) {
            const slot = inv.getItem(i);
            if (!slot || slot.keepOnDeath == true)
                continue;
            if (!ids) {
                items.push({ item: slot, slot: i });
                continue;
            }
            if (ids.includes(slot.typeId))
                items.push({ item: slot, slot: i });
        }
        return items;
    }
    getEmptySlots(inv) {
        const empty = [];
        for (let i = 0; i < inv.size; i++) {
            if (inv.getItem(i) == undefined)
                empty.push(i);
        }
        return empty;
    }
    transferItemsToGravestone(playerInv, graveInv) {
        for (let i = 0; i < playerInv.size; i++) {
            const item = playerInv.getItem(i);
            if (item?.keepOnDeath != true) {
                graveInv.setItem(i, item);
                playerInv.setItem(i, undefined);
            }
        }
    }
    transferItemsToInventory(playerInv, graveInv) {
        for (let i = 0; i < playerInv.size; i++) {
            const graveSlot = graveInv.getItem(i);
            if (graveSlot != undefined)
                playerInv.swapItems(i, i, graveInv);
        }
        apiEquippable.transferItemsToInventoryOldItems(graveInv, playerInv);
    }
    async transferItemsToInventoryOldItems(graveInv, playerInv) {
        await system.waitTicks(1);
        for (let i = 0; i < playerInv.size; i++) {
            if (playerInv.emptySlotsCount < 1)
                return;
            const graveSlot = graveInv.getItem(i);
            if (graveSlot == undefined)
                continue;
            playerInv.addItem(graveSlot);
            graveInv.setItem(i, undefined);
        }
    }
    async dropInventory(id) {
        const entity = world.getEntity(id);
        if (!entity)
            return false;
        const inv = entity.getComponent("inventory")?.container;
        if (!inv)
            return false;
        for (let i = 0; i < inv.size; i++) {
            const item = inv.getItem(i);
            if (item)
                entity.dimension.spawnItem(item, entity.location);
        }
        return true;
    }
    setItem(entity, item, slot) {
        const inventory = entity.getComponent("inventory")?.container;
        if (!inventory)
            return;
        if (slot > inventory.size)
            return;
        inventory.setItem(slot, item);
    }
};
