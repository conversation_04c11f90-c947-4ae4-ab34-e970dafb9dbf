import { world } from "@minecraft/server";
import { apiSafeArea } from "./lib/block/safeArea";
world.afterEvents.itemUse.subscribe(({ itemStack: item, source: entity }) => {
    const player = entity;
    if (!player.hasTag("dev"))
        return;
    if (item.typeId == "minecraft:netherite_shovel")
        world.sendMessage(`${player.getDynamicPropertyIds().filter(value => value.startsWith("gravestone_death_path:death_path_point_")).map(value => (value.replace("gravestone_death_path:death_path_point_", ""))).sort()}`);
    if (item.typeId == "minecraft:stick") {
        player.clearDynamicProperties();
        world.sendMessage(`Clear Dynamics`);
    }
    if (item.typeId == "minecraft:golden_carrot") {
        world.sendMessage(`${JSON.stringify(apiSafeArea.getSafePos(player.location, player.dimension))}`);
    }
    // player.sendMessage(`${player.level}`)
});
