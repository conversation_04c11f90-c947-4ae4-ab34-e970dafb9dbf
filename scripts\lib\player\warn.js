import { system } from "@minecraft/server";
export const apiWarn = new class apiWarn {
    notify(player, message, options) {
        const type = options && options.type ? options.type : "chat";
        const execute = notifyTypes[type];
        if (execute)
            execute(player, message);
        system.runTimeout(() => {
            if (options?.sound)
                player.playSound(options.sound, { volume: options.volume });
        }, options?.delaySound);
    }
};
const notifyTypes = new class notifyTypes {
    "chat"(player, message) { player.sendMessage(typeof message == "string" ? { translate: message } : message); }
    "action_bar"(player, message) { player.onScreenDisplay.setActionBar(message); }
    "title"(player, message) { player.onScreenDisplay.setTitle(message); }
};
